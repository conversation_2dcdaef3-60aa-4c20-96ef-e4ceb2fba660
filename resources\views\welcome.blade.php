<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="TikPro - Platform tiket event terpercaya di Indonesia. Temukan dan beli tiket event favorit Anda dengan mudah dan aman.">
    <meta name="keywords" content="tiket event, konser, festival, seminar, workshop, TikPro, Indonesia">
    <meta name="author" content="TikPro">
    <meta name="theme-color" content="#A8D5BA">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="TikPro - Platform Tiket Event Terpercaya">
    <meta property="og:description" content="Temukan dan beli tiket event favorit Anda dengan mudah dan aman di TikPro">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url('/') }}">
    <meta property="og:image" content="{{ asset('images/og-image.jpg') }}">
    <meta property="og:site_name" content="TikPro">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="TikPro - Platform Tiket Event Terpercaya">
    <meta name="twitter:description" content="Temukan dan beli tiket event favorit Anda dengan mudah dan aman di TikPro">
    <meta name="twitter:image" content="{{ asset('images/og-image.jpg') }}">
    <meta name="twitter:site" content="@tikpro_id">

    <title>TikPro - Platform Tiket Event Terpercaya Indonesia</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=DM+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "TikPro",
        "url": "{{ url('/') }}",
        "logo": "{{ asset('images/logo.png') }}",
        "description": "Platform tiket event terpercaya di Indonesia",
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "ID",
            "addressLocality": "Jakarta"
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+62-812-3456-7890",
            "contactType": "customer service"
        }
    }
    </script>

    <style>
        :root {
            --primary: #A8D5BA;
            --secondary: #F4A261;
            --accent: #E76F51;
            --dark: #264653;
            --light: #F1FAEE;
            --success: #10B981;
            --warning: #F59E0B;
            --error: #EF4444;
            --info: #3B82F6;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--accent);
        }

        /* Loading animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Enhanced gradients */
        .gradient-bg {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
        }

        .gradient-text {
            background: linear-gradient(135deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Color utilities */
        .text-primary { color: var(--primary); }
        .text-secondary { color: var(--secondary); }
        .text-accent { color: var(--accent); }
        .text-dark { color: var(--dark); }
        .text-success { color: var(--success); }
        .text-warning { color: var(--warning); }
        .text-error { color: var(--error); }
        .text-info { color: var(--info); }

        .bg-primary { background-color: var(--primary); }
        .bg-secondary { background-color: var(--secondary); }
        .bg-accent { background-color: var(--accent); }
        .bg-dark { background-color: var(--dark); }
        .bg-light { background-color: var(--light); }
        .bg-success { background-color: var(--success); }
        .bg-warning { background-color: var(--warning); }
        .bg-error { background-color: var(--error); }
        .bg-info { background-color: var(--info); }

        .border-primary { border-color: var(--primary); }

        /* Enhanced hero pattern */
        .hero-pattern {
            background-image:
                radial-gradient(circle at 25% 25%, rgba(168, 213, 186, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(244, 162, 97, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, rgba(168, 213, 186, 0.05) 0%, rgba(244, 162, 97, 0.05) 100%);
        }

        /* Advanced animations */
        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        .floating-delayed {
            animation: floating 3s ease-in-out infinite;
            animation-delay: 1s;
        }

        .floating-slow {
            animation: floating 4s ease-in-out infinite;
            animation-delay: 2s;
        }

        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }

        .pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .bounce-in {
            animation: bounceIn 0.8s ease-out;
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        /* Glassmorphism effect */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Hover effects */
        .hover-lift {
            transition: all 0.3s ease;
        }

        .hover-lift:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        /* Parallax effect */
        .parallax {
            transform: translateZ(0);
            will-change: transform;
        }

        /* Custom button styles */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(168, 213, 186, 0.4);
        }

        /* Typing animation */
        .typing-animation {
            overflow: hidden;
            border-right: 3px solid var(--primary);
            white-space: nowrap;
            animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: var(--primary); }
        }

        /* Particle background */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            background: var(--primary);
            border-radius: 50%;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(30px) rotate(240deg); }
        }

        /* Card hover effects */
        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        /* Event card styles */
        .event-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .event-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .event-image {
            height: 200px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            position: relative;
            overflow: hidden;
        }

        .event-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.2);
            transition: opacity 0.3s ease;
        }

        .event-card:hover .event-image::before {
            opacity: 0;
        }

        /* Category badge */
        .category-badge {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 12px;
            font-weight: 600;
            color: var(--dark);
        }

        /* Price tag */
        .price-tag {
            background: linear-gradient(135deg, var(--success), #059669);
            color: white;
            padding: 8px 16px;
            border-radius: 12px;
            font-weight: 700;
            font-size: 18px;
        }

        /* Feature icon */
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .feature-icon:hover {
            transform: scale(1.1) rotate(5deg);
        }

        /* Testimonial card */
        .testimonial-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 20px;
            font-size: 100px;
            color: var(--primary);
            opacity: 0.1;
            font-family: serif;
        }

        /* Newsletter section */
        .newsletter-bg {
            background: linear-gradient(135deg, var(--dark) 0%, var(--primary) 100%);
            position: relative;
            overflow: hidden;
        }

        .newsletter-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .hero-pattern {
                background-image:
                    radial-gradient(circle at 25% 25%, rgba(168, 213, 186, 0.05) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(244, 162, 97, 0.05) 0%, transparent 50%);
            }

            .event-card, .testimonial-card {
                background: #1f2937;
                color: white;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .floating, .floating-delayed, .floating-slow, .pulse-slow, .bounce-in {
                animation: none;
            }

            .typing-animation {
                animation: none;
                border-right: none;
            }

            .card-hover:hover, .event-card:hover {
                transform: none;
            }
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .hero-pattern {
                background-size: 200% 200%;
            }

            .floating, .floating-delayed, .floating-slow {
                display: none;
            }
        }
    </style>
</head>

<body class="antialiased font-poppins" x-data="{ mobileMenuOpen: false, darkMode: false, isLoading: true }" x-init="setTimeout(() => isLoading = false, 1500)">
    <!-- Loading Screen -->
    <div x-show="isLoading"
         x-transition:leave="transition ease-in duration-500"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="loading-overlay">
        <div class="text-center">
            <div class="loading-spinner mb-4"></div>
            <div class="text-white text-xl font-semibold">TikPro</div>
            <div class="text-white/80 text-sm">Loading your experience...</div>
        </div>
    </div>

    <!-- Scroll to Top Button -->
    <button x-show="window.pageYOffset > 300"
            @click="window.scrollTo({top: 0, behavior: 'smooth'})"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-90"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-90"
            class="fixed bottom-8 right-8 z-40 bg-gradient-to-r from-primary to-secondary text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110"
            aria-label="Scroll to top">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
        </svg>
    </button>

    <!-- Progress Bar -->
    <div class="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
        <div class="h-full bg-gradient-to-r from-primary to-secondary transition-all duration-300 ease-out"
             :style="`width: ${(window.pageYOffset / (document.body.scrollHeight - window.innerHeight)) * 100}%`"></div>
    </div>

    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-md shadow-lg fixed w-full top-0 z-50 transition-all duration-300"
         x-data="{ scrolled: false }"
         @scroll.window="scrolled = window.pageYOffset > 50"
         :class="{ 'bg-white/98 shadow-xl': scrolled }">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                        </svg>
                    </div>
                    <h1 class="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                        TikPro
                    </h1>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Beranda</a>
                    <a href="#events" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Event</a>
                    <a href="#features" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Fitur</a>
                    <a href="#about" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Tentang</a>
                    <a href="#contact" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Kontak</a>
                </div>

                <!-- Auth Buttons -->
                <div class="hidden md:flex items-center space-x-4">
                    @if (Route::has('login'))
                        @auth
                            <a href="{{ url('/dashboard') }}"
                               class="text-gray-700 hover:text-primary px-4 py-2 rounded-lg transition-colors duration-200 font-medium">
                                Dashboard
                            </a>
                        @else
                            <a href="{{ route('login') }}"
                               class="text-gray-700 hover:text-primary px-4 py-2 rounded-lg transition-colors duration-200 font-medium">
                                Masuk
                            </a>
                            @if (Route::has('register'))
                                <a href="{{ route('register') }}"
                                   class="bg-gradient-to-r from-primary to-secondary text-white hover:shadow-lg px-6 py-2 rounded-lg transition-all duration-200 font-medium transform hover:scale-105">
                                    Daftar
                                </a>
                            @endif
                        @endauth
                    @endif
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button @click="mobileMenuOpen = !mobileMenuOpen"
                            class="text-gray-700 hover:text-primary p-2 rounded-lg transition-colors duration-200">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div x-show="mobileMenuOpen"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform -translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-2"
             class="md:hidden bg-white border-t border-gray-200">
            <div class="px-4 py-6 space-y-4">
                <a href="#home" class="block text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Beranda</a>
                <a href="#events" class="block text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Event</a>
                <a href="#features" class="block text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Fitur</a>
                <a href="#about" class="block text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Tentang</a>
                <a href="#contact" class="block text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Kontak</a>

                @if (Route::has('login'))
                    <div class="pt-4 border-t border-gray-200 space-y-2">
                        @auth
                            <a href="{{ url('/dashboard') }}"
                               class="block w-full text-center bg-gray-100 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                                Dashboard
                            </a>
                        @else
                            <a href="{{ route('login') }}"
                               class="block w-full text-center bg-gray-100 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                                Masuk
                            </a>
                            @if (Route::has('register'))
                                <a href="{{ route('register') }}"
                                   class="block w-full text-center bg-gradient-to-r from-primary to-secondary text-white px-4 py-2 rounded-lg transition-all duration-200">
                                    Daftar
                                </a>
                            @endif
                        @endauth
                    </div>
                @endif
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center hero-pattern pt-16 relative overflow-hidden">
        <!-- Particle Background -->
        <div class="particles">
            <div class="particle" style="left: 10%; top: 20%; width: 4px; height: 4px; animation-delay: 0s;"></div>
            <div class="particle" style="left: 20%; top: 80%; width: 6px; height: 6px; animation-delay: 1s;"></div>
            <div class="particle" style="left: 60%; top: 30%; width: 3px; height: 3px; animation-delay: 2s;"></div>
            <div class="particle" style="left: 80%; top: 70%; width: 5px; height: 5px; animation-delay: 3s;"></div>
            <div class="particle" style="left: 30%; top: 10%; width: 4px; height: 4px; animation-delay: 4s;"></div>
            <div class="particle" style="left: 70%; top: 90%; width: 3px; height: 3px; animation-delay: 5s;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10">
            <div class="text-center">
                <!-- Hero Content -->
                <div class="max-w-4xl mx-auto" data-aos="fade-up">
                    <!-- Animated Badge -->
                    <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-primary font-medium mb-8 bounce-in">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                        Platform #1 di Indonesia
                    </div>

                    <h1 class="text-5xl md:text-7xl font-bold text-dark mb-6 leading-tight">
                        <span class="block">Temukan Event</span>
                        <span class="block gradient-text typing-animation" style="animation-delay: 1s;">
                            Impianmu
                        </span>
                    </h1>
                    <p class="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="300">
                        Platform tiket event terpercaya di Indonesia. Beli tiket konser, festival, seminar, dan workshop dengan mudah dan aman.
                    </p>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12" data-aos="fade-up" data-aos-delay="500">
                        <a href="#events"
                           class="btn-primary px-8 py-4 rounded-xl font-semibold text-lg inline-flex items-center space-x-2 group">
                            <span>Jelajahi Event</span>
                            <svg class="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                            </svg>
                        </a>
                        <a href="#about"
                           class="glass border-2 border-primary/30 text-primary px-8 py-4 rounded-xl font-semibold text-lg hover:bg-primary hover:text-white transition-all duration-300 inline-flex items-center space-x-2 group">
                            <svg class="w-5 h-5 transition-transform duration-300 group-hover:rotate-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span>Pelajari Lebih Lanjut</span>
                        </a>
                    </div>

                    <!-- Search Bar -->
                    <div class="max-w-2xl mx-auto mb-12" data-aos="fade-up" data-aos-delay="600">
                        <div class="relative">
                            <input type="text"
                                   placeholder="Cari event favorit Anda..."
                                   class="w-full px-6 py-4 pl-12 pr-16 rounded-2xl border-2 border-gray-200 focus:border-primary focus:outline-none transition-all duration-300 text-lg glass">
                            <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                            <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-primary to-secondary text-white px-6 py-2 rounded-xl hover:shadow-lg transition-all duration-300">
                                Cari
                            </button>
                        </div>
                    </div>

                    <!-- Stats -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="700">
                        <div class="text-center group">
                            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 hover-lift">
                                <div class="text-3xl md:text-4xl font-bold gradient-text mb-2 counter" data-target="1000">0</div>
                                <div class="text-gray-600 font-medium">Event Tersedia</div>
                            </div>
                        </div>
                        <div class="text-center group">
                            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 hover-lift">
                                <div class="text-3xl md:text-4xl font-bold gradient-text mb-2 counter" data-target="50">0</div>
                                <div class="text-gray-600 font-medium">K+ Pengguna Aktif</div>
                            </div>
                        </div>
                        <div class="text-center group">
                            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 hover-lift">
                                <div class="text-3xl md:text-4xl font-bold gradient-text mb-2 counter" data-target="100">0</div>
                                <div class="text-gray-600 font-medium">K+ Tiket Terjual</div>
                            </div>
                        </div>
                        <div class="text-center group">
                            <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all duration-300 hover-lift">
                                <div class="text-3xl md:text-4xl font-bold gradient-text mb-2 counter" data-target="99">0</div>
                                <div class="text-gray-600 font-medium">% Kepuasan</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Floating Elements -->
                <div class="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full floating glass" data-aos="fade-right" data-aos-delay="600">
                    <div class="w-full h-full rounded-full bg-gradient-to-br from-primary/30 to-transparent"></div>
                </div>
                <div class="absolute top-40 right-10 w-16 h-16 bg-gradient-to-br from-secondary/20 to-accent/20 rounded-full floating-delayed glass" data-aos="fade-left" data-aos-delay="800">
                    <div class="w-full h-full rounded-full bg-gradient-to-br from-secondary/30 to-transparent"></div>
                </div>
                <div class="absolute bottom-20 left-20 w-12 h-12 bg-gradient-to-br from-accent/20 to-primary/20 rounded-full floating-slow glass" data-aos="fade-right" data-aos-delay="1000">
                    <div class="w-full h-full rounded-full bg-gradient-to-br from-accent/30 to-transparent"></div>
                </div>
                <div class="absolute top-1/2 right-20 w-8 h-8 bg-gradient-to-br from-primary/15 to-secondary/15 rounded-full floating" data-aos="fade-left" data-aos-delay="1200">
                    <div class="w-full h-full rounded-full bg-gradient-to-br from-primary/25 to-transparent"></div>
                </div>
                <div class="absolute bottom-40 right-40 w-6 h-6 bg-gradient-to-br from-secondary/15 to-accent/15 rounded-full floating-delayed" data-aos="fade-up" data-aos-delay="1400">
                    <div class="w-full h-full rounded-full bg-gradient-to-br from-secondary/25 to-transparent"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-light">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold text-dark mb-6">
                    Mengapa Memilih
                    <span class="gradient-text">TikPro?</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Kami menyediakan platform yang aman, mudah, dan terpercaya untuk semua kebutuhan tiket event Anda
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 hover-lift relative overflow-hidden" data-aos="fade-up" data-aos-delay="100">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/10 to-transparent rounded-full transform translate-x-16 -translate-y-16"></div>
                    <div class="relative z-10">
                        <div class="feature-icon bg-gradient-to-br from-primary to-secondary">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-dark mb-4 group-hover:text-primary transition-colors duration-300">Aman & Terpercaya</h3>
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Sistem keamanan berlapis dan enkripsi data untuk melindungi informasi pribadi dan transaksi Anda.
                        </p>
                        <div class="flex items-center text-primary font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="mr-2">Pelajari lebih lanjut</span>
                            <svg class="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 hover-lift relative overflow-hidden" data-aos="fade-up" data-aos-delay="200">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-secondary/10 to-transparent rounded-full transform translate-x-16 -translate-y-16"></div>
                    <div class="relative z-10">
                        <div class="feature-icon bg-gradient-to-br from-secondary to-accent">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-dark mb-4 group-hover:text-secondary transition-colors duration-300">Proses Cepat</h3>
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Pembelian tiket dalam hitungan detik dengan sistem pembayaran yang terintegrasi dan mudah digunakan.
                        </p>
                        <div class="flex items-center text-secondary font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="mr-2">Pelajari lebih lanjut</span>
                            <svg class="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 hover-lift relative overflow-hidden" data-aos="fade-up" data-aos-delay="300">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-accent/10 to-transparent rounded-full transform translate-x-16 -translate-y-16"></div>
                    <div class="relative z-10">
                        <div class="feature-icon bg-gradient-to-br from-accent to-primary">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-dark mb-4 group-hover:text-accent transition-colors duration-300">E-Ticket Digital</h3>
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Tiket digital dengan QR code yang dapat diakses kapan saja melalui smartphone Anda.
                        </p>
                        <div class="flex items-center text-accent font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="mr-2">Pelajari lebih lanjut</span>
                            <svg class="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Feature 4 -->
                <div class="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 hover-lift relative overflow-hidden" data-aos="fade-up" data-aos-delay="400">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-info/10 to-transparent rounded-full transform translate-x-16 -translate-y-16"></div>
                    <div class="relative z-10">
                        <div class="feature-icon bg-gradient-to-br from-info to-primary">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-dark mb-4 group-hover:text-info transition-colors duration-300">Customer Support 24/7</h3>
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Tim support yang siap membantu Anda kapan saja melalui berbagai channel komunikasi.
                        </p>
                        <div class="flex items-center text-info font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="mr-2">Pelajari lebih lanjut</span>
                            <svg class="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Feature 5 -->
                <div class="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 hover-lift relative overflow-hidden" data-aos="fade-up" data-aos-delay="500">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-success/10 to-transparent rounded-full transform translate-x-16 -translate-y-16"></div>
                    <div class="relative z-10">
                        <div class="feature-icon bg-gradient-to-br from-success to-secondary">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-dark mb-4 group-hover:text-success transition-colors duration-300">Harga Terjangkau</h3>
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Biaya admin yang transparan dan kompetitif tanpa biaya tersembunyi.
                        </p>
                        <div class="flex items-center text-success font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="mr-2">Pelajari lebih lanjut</span>
                            <svg class="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Feature 6 -->
                <div class="group bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 hover-lift relative overflow-hidden" data-aos="fade-up" data-aos-delay="600">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-warning/10 to-transparent rounded-full transform translate-x-16 -translate-y-16"></div>
                    <div class="relative z-10">
                        <div class="feature-icon bg-gradient-to-br from-warning to-accent">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-dark mb-4 group-hover:text-warning transition-colors duration-300">Event Berkualitas</h3>
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Kurasi event terbaik dari organizer terpercaya di seluruh Indonesia.
                        </p>
                        <div class="flex items-center text-warning font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <span class="mr-2">Pelajari lebih lanjut</span>
                            <svg class="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Events Preview Section -->
    <section id="events" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold text-dark mb-6">
                    Event
                    <span class="gradient-text">Terpopuler</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Jangan lewatkan event-event menarik yang sedang trending dan akan segera dimulai
                </p>
            </div>

            <!-- Sample Events Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- Event Card 1 -->
                <div class="event-card card-hover" data-aos="fade-up" data-aos-delay="100">
                    <div class="event-image">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-white text-center">
                                <svg class="w-16 h-16 mx-auto mb-4 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"/>
                                </svg>
                                <h4 class="text-xl font-bold">Jakarta Music Festival</h4>
                            </div>
                        </div>
                        <div class="absolute bottom-4 left-4">
                            <span class="category-badge">Musik</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-dark mb-2">Jakarta Music Festival 2024</h3>
                        <p class="text-gray-600 mb-4 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            Jakarta Convention Center
                        </p>
                        <div class="flex justify-between items-center">
                            <span class="price-tag">Rp 150.000</span>
                            <span class="text-sm text-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                15 Des 2024
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Event Card 2 -->
                <div class="event-card card-hover" data-aos="fade-up" data-aos-delay="200">
                    <div class="event-image bg-gradient-to-br from-secondary to-accent">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-white text-center">
                                <svg class="w-16 h-16 mx-auto mb-4 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                                <h4 class="text-xl font-bold">Digital Marketing</h4>
                            </div>
                        </div>
                        <div class="absolute bottom-4 left-4">
                            <span class="category-badge">Workshop</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-dark mb-2">Digital Marketing Masterclass</h3>
                        <p class="text-gray-600 mb-4 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            Balai Kartini Jakarta
                        </p>
                        <div class="flex justify-between items-center">
                            <span class="price-tag">Rp 299.000</span>
                            <span class="text-sm text-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                20 Des 2024
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Event Card 3 -->
                <div class="event-card card-hover" data-aos="fade-up" data-aos-delay="300">
                    <div class="event-image bg-gradient-to-br from-accent to-primary">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-white text-center">
                                <svg class="w-16 h-16 mx-auto mb-4 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 15.546c-.523 0-1.046.151-1.5.454a2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.704 2.704 0 00-3 0 2.704 2.704 0 01-3 0 2.701 2.701 0 00-1.5-.454M9 6v2m3-2v2m3-2v2M9 3h.01M12 3h.01M15 3h.01M21 21v-7a2 2 0 00-2-2H5a2 2 0 00-2 2v7h18zm-3-9v-2a2 2 0 00-2-2H8a2 2 0 00-2 2v2h12z"/>
                                </svg>
                                <h4 class="text-xl font-bold">Food Festival</h4>
                            </div>
                        </div>
                        <div class="absolute bottom-4 left-4">
                            <span class="category-badge">Festival</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-dark mb-2">Bali Food & Culture Festival</h3>
                        <p class="text-gray-600 mb-4 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            Sanur Beach, Bali
                        </p>
                        <div class="flex justify-between items-center">
                            <span class="price-tag">Rp 75.000</span>
                            <span class="text-sm text-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                25 Des 2024
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- View All Events Button -->
            <div class="text-center" data-aos="fade-up" data-aos-delay="400">
                <a href="#" class="btn-primary px-8 py-4 rounded-xl font-semibold text-lg inline-flex items-center space-x-2 group">
                    <span>Lihat Semua Event</span>
                    <svg class="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-light">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                <!-- Content -->
                <div data-aos="fade-right">
                    <h2 class="text-4xl md:text-5xl font-bold text-dark mb-6">
                        Tentang
                        <span class="gradient-text">TikPro</span>
                    </h2>
                    <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                        TikPro adalah platform tiket event terdepan di Indonesia yang menghubungkan Anda dengan berbagai event menarik di seluruh nusantara. Kami berkomitmen untuk memberikan pengalaman terbaik dalam pembelian tiket event.
                    </p>

                    <div class="space-y-4 mb-8">
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                            </div>
                            <span class="text-gray-700 text-lg">Platform terpercaya sejak 2020</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                            </div>
                            <span class="text-gray-700 text-lg">Lebih dari 1000+ event tersedia</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                            </div>
                            <span class="text-gray-700 text-lg">Customer support 24/7</span>
                        </div>
                    </div>

                    <a href="#contact" class="btn-primary px-8 py-4 rounded-xl font-semibold text-lg inline-flex items-center space-x-2 group">
                        <span>Hubungi Kami</span>
                        <svg class="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                        </svg>
                    </a>
                </div>

                <!-- Image/Illustration -->
                <div class="relative" data-aos="fade-left">
                    <div class="w-full h-96 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-3xl flex items-center justify-center relative overflow-hidden">
                        <div class="text-center z-10">
                            <div class="w-32 h-32 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-dark mb-2">TikPro</h3>
                            <p class="text-gray-600">Your Event Ticket Partner</p>
                        </div>
                        <!-- Decorative elements -->
                        <div class="absolute top-10 left-10 w-16 h-16 bg-primary/10 rounded-full"></div>
                        <div class="absolute bottom-10 right-10 w-20 h-20 bg-secondary/10 rounded-full"></div>
                        <div class="absolute top-1/2 right-20 w-12 h-12 bg-accent/10 rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter-bg py-20 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    Jangan Lewatkan Event Terbaru
                </h2>
                <p class="text-xl text-white/80 mb-8 max-w-3xl mx-auto">
                    Berlangganan newsletter kami dan dapatkan informasi event terbaru, promo menarik, dan penawaran eksklusif langsung di inbox Anda.
                </p>

                <div class="max-w-md mx-auto">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <input type="email"
                               placeholder="Masukkan email Anda..."
                               class="flex-1 px-6 py-4 rounded-xl border-0 focus:outline-none focus:ring-2 focus:ring-white/50 text-lg">
                        <button class="bg-white text-dark px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors duration-300">
                            Berlangganan
                        </button>
                    </div>
                    <p class="text-white/60 text-sm mt-4">
                        Kami menghormati privasi Anda. Unsubscribe kapan saja.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="bg-dark text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                <!-- Company Info -->
                <div data-aos="fade-up" data-aos-delay="100">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold">TikPro</h3>
                    </div>
                    <p class="text-gray-300 mb-6">
                        Platform tiket event terpercaya di Indonesia. Temukan dan beli tiket event favorit Anda dengan mudah dan aman.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center hover:bg-primary transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center hover:bg-primary transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center hover:bg-primary transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
                            </svg>
                        </a>
                        <a href="#" class="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center hover:bg-primary transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12.5.75C6.146.75 1 5.896 1 12.25c0 5.089 3.292 9.387 7.863 10.91-.11-.937-.227-2.482.025-3.566.217-.932 1.405-5.956 1.405-5.956s-.359-.719-.359-1.782c0-1.668.967-2.914 2.171-2.914 1.023 0 1.518.769 1.518 1.69 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146 1.124.347 2.317.544 3.571.544 6.624 0 11.99-5.367 11.99-11.988C24.5 5.896 19.354.75 12.5.75z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div data-aos="fade-up" data-aos-delay="200">
                    <h4 class="text-lg font-semibold mb-6">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="#home" class="text-gray-300 hover:text-primary transition-colors duration-200">Beranda</a></li>
                        <li><a href="#events" class="text-gray-300 hover:text-primary transition-colors duration-200">Event</a></li>
                        <li><a href="#features" class="text-gray-300 hover:text-primary transition-colors duration-200">Fitur</a></li>
                        <li><a href="#about" class="text-gray-300 hover:text-primary transition-colors duration-200">Tentang</a></li>
                        <li><a href="#contact" class="text-gray-300 hover:text-primary transition-colors duration-200">Kontak</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div data-aos="fade-up" data-aos-delay="300">
                    <h4 class="text-lg font-semibold mb-6">Kategori Event</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-300 hover:text-primary transition-colors duration-200">Musik & Konser</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-primary transition-colors duration-200">Workshop & Seminar</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-primary transition-colors duration-200">Festival & Pameran</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-primary transition-colors duration-200">Olahraga</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-primary transition-colors duration-200">Teknologi</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div data-aos="fade-up" data-aos-delay="400">
                    <h4 class="text-lg font-semibold mb-6">Hubungi Kami</h4>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span class="text-gray-300"><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span class="text-gray-300">+62 812-3456-7890</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="text-gray-300">Jakarta, Indonesia</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span class="text-gray-300">24/7 Customer Support</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Footer -->
            <div class="border-t border-gray-700 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-gray-300 text-sm mb-4 md:mb-0">
                        © 2024 TikPro. All rights reserved. Built with Laravel v{{ Illuminate\Foundation\Application::VERSION }}
                    </div>
                    <div class="flex space-x-6 text-sm">
                        <a href="#" class="text-gray-300 hover:text-primary transition-colors duration-200">Privacy Policy</a>
                        <a href="#" class="text-gray-300 hover:text-primary transition-colors duration-200">Terms of Service</a>
                        <a href="#" class="text-gray-300 hover:text-primary transition-colors duration-200">Cookie Policy</a>
                        <a href="#" class="text-gray-300 hover:text-primary transition-colors duration-200">Sitemap</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- AOS Animation Script -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Counter Animation
        function animateCounter(element, target, duration = 2000) {
            let start = 0;
            const increment = target / (duration / 16);
            const timer = setInterval(() => {
                start += increment;
                if (start >= target) {
                    element.textContent = target;
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(start);
                }
            }, 16);
        }

        // Intersection Observer for counters
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    const target = parseInt(counter.getAttribute('data-target'));
                    animateCounter(counter, target);
                    counterObserver.unobserve(counter);
                }
            });
        }, { threshold: 0.5 });

        // Observe all counters
        document.querySelectorAll('.counter').forEach(counter => {
            counterObserver.observe(counter);
        });

        // Smooth scroll for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Progress bar update
        function updateProgressBar() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            const progressBar = document.querySelector('.h-full.bg-gradient-to-r');
            if (progressBar) {
                progressBar.style.width = scrollPercent + '%';
            }
        }

        window.addEventListener('scroll', updateProgressBar);

        // Search functionality
        const searchInput = document.querySelector('input[placeholder="Cari event favorit Anda..."]');
        const searchButton = document.querySelector('button:contains("Cari")');

        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const searchTerm = this.value;
                    if (searchTerm.trim()) {
                        // Simulate search - in real app, this would redirect to search results
                        alert(`Mencari event: "${searchTerm}"`);
                    }
                }
            });
        }

        // Newsletter subscription
        const newsletterForm = document.querySelector('input[type="email"]');
        const subscribeButton = document.querySelector('button:contains("Berlangganan")');

        if (subscribeButton) {
            subscribeButton.addEventListener('click', function() {
                const email = newsletterForm?.value;
                if (email && email.includes('@')) {
                    alert(`Terima kasih! Kami akan mengirim update ke ${email}`);
                    if (newsletterForm) newsletterForm.value = '';
                } else {
                    alert('Mohon masukkan email yang valid');
                }
            });
        }

        // Add loading animation to buttons
        document.querySelectorAll('.btn-primary').forEach(button => {
            button.addEventListener('click', function(e) {
                if (this.href && this.href.includes('#')) {
                    return; // Let smooth scroll handle it
                }

                // Add loading state
                const originalText = this.innerHTML;
                this.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading...
                `;

                // Reset after 2 seconds (simulate loading)
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            });
        });

        // Keyboard navigation support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });

        // Performance optimization: Debounce scroll events
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Apply debouncing to scroll events
        window.addEventListener('scroll', debounce(updateProgressBar, 10));

        // Add focus styles for accessibility
        const style = document.createElement('style');
        style.textContent = `
            .keyboard-navigation *:focus {
                outline: 2px solid var(--primary) !important;
                outline-offset: 2px !important;
            }
        `;
        document.head.appendChild(style);

        // Lazy loading for images (if any are added later)
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Service Worker registration for PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed');
                    });
            });
        }

        // Dark mode toggle (optional feature)
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
        }

        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.documentElement.classList.add('dark');
        }

        // Add event listeners for dynamic content
        document.addEventListener('DOMContentLoaded', function() {
            // Add click tracking for analytics (placeholder)
            document.querySelectorAll('a, button').forEach(element => {
                element.addEventListener('click', function() {
                    // Analytics tracking would go here
                    console.log('Element clicked:', this.textContent.trim());
                });
            });

            // Add form validation
            document.querySelectorAll('input[type="email"]').forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.value && !this.value.includes('@')) {
                        this.style.borderColor = 'var(--error)';
                    } else {
                        this.style.borderColor = '';
                    }
                });
            });
        });

        console.log('🎉 TikPro website loaded successfully!');
        console.log('📱 Platform: ' + navigator.platform);
        console.log('🌐 User Agent: ' + navigator.userAgent);
    </script>
</body>
</html>