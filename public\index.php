@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-primary to-accent rounded-3xl p-8 mb-8" data-aos="fade-up">
        <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">Temukan Event Terbaik</h1>
        <p class="text-white/90 text-lg mb-6">Jelajahi berbagai event menarik dan dapatkan tiketmu sekarang!</p>
        
        <!-- Search Bar -->
        <div class="relative">
            <input type="text" 
                   class="w-full px-6 py-4 rounded-full bg-white/95 shadow-lg focus:outline-none focus:ring-2 focus:ring-accent"
                   placeholder="Cari event, konser, atau seminar...">
            <button class="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-primary">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
            </button>
        </div>
    </div>

    <!-- Category Filters -->
    <div class="flex overflow-x-auto space-x-4 mb-8 pb-4 scrollbar-hide" data-aos="fade-up" data-aos-delay="100">
        <button class="px-6 py-3 rounded-full bg-primary text-white shadow-md flex-shrink-0 hover:bg-primary/90 transition">
            Semua
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Konser
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Festival
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Seminar
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Workshop
        </button>
        <button class="px-6 py-3 rounded-full bg-white text-gray-700 shadow-md flex-shrink-0 hover:bg-gray-50 transition">
            Olahraga
        </button>
    </div>

    <!-- Events Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-aos="fade-up" data-aos-delay="200">
        @forelse($events as $event)
        <div class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
            <img src="{{ $event->poster_url }}" alt="{{ $event->title }}" class="w-full h-48 object-cover">
            <div class="p-6">
                <div class="flex items-center space-x-2 mb-4">
                    <span class="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">{{ $event->category }}</span>
                    <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">{{ $event->formatted_date }}</span>
                </div>
                <h3 class="text-xl font-bold mb-2">{{ $event->title }}</h3>
                <p class="text-gray-600 mb-4">{{ $event->location }}</p>
                <div class="flex justify-between items-center">
                    <span class="text-lg font-bold text-primary">Rp {{ number_format($event->price, 0, ',', '.') }}</span>
                    <a href="{{ route('events.show', $event) }}" 
                       class="px-6 py-2 bg-primary text-white rounded-full hover:bg-primary/90 transition">
                        Detail
                    </a>
                </div>
            </div>
        </div>
        @empty
        <div class="col-span-full text-center py-12">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 12h.01M12 12h.01M12 12h.01M12 12h.01M12 12h.01"/>
            </svg>
            <p class="text-gray-500 text-lg">Belum ada event yang tersedia</p>
        </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($events->hasPages())
    <div class="mt-8" data-aos="fade-up" data-aos-delay="300">
        {{ $events->links() }}
    </div>
    @endif
</div>

<!-- Install PWA Prompt -->
<div id="installPrompt" class="hidden fixed bottom-20 left-4 right-4 md:left-auto md:right-4 md:bottom-4 md:w-96 bg-white rounded-2xl shadow-2xl p-6">
    <h4 class="text-lg font-bold mb-2">Install TikPro App</h4>
    <p class="text-gray-600 mb-4">Install aplikasi TikPro untuk pengalaman yang lebih baik dan akses tiket offline.</p>
    <div class="flex justify-end space-x-4">
        <button onclick="this.parentElement.parentElement.classList.add('hidden')" class="text-gray-500 hover:text-gray-700">
            Nanti
        </button>
        <button id="installButton" class="px-6 py-2 bg-primary text-white rounded-full hover:bg-primary/90 transition">
            Install
        </button>
    </div>
</div>
@endsection