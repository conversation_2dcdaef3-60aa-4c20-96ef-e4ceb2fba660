@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Theme Colors */
:root {
    --color-primary: #A8D5BA;
    --color-secondary: #F7F7F7;
    --color-tertiary: #D6D6D6;
    --color-accent: #C7EACB;
}

/* Custom Components */
@layer components {
    /* Buttons */
    .btn-primary {
        @apply bg-primary text-white px-4 py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors;
    }

    .btn-secondary {
        @apply bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors;
    }

    /* Cards */
    .card {
        @apply bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300;
    }

    .card-header {
        @apply p-4 border-b border-gray-100;
    }

    .card-body {
        @apply p-4;
    }

    /* Form Elements */
    .form-input {
        @apply w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-1;
    }

    /* Badges */
    .badge {
        @apply px-2 py-1 text-xs rounded-full;
    }

    .badge-primary {
        @apply bg-primary/10 text-primary;
    }

    .badge-success {
        @apply bg-green-100 text-green-700;
    }

    .badge-warning {
        @apply bg-yellow-100 text-yellow-700;
    }

    .badge-danger {
        @apply bg-red-100 text-red-700;
    }

    /* Navigation */
    .nav-link {
        @apply flex items-center space-x-2 px-4 py-2 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors;
    }

    .nav-link.active {
        @apply bg-primary/10 text-primary;
    }

    /* Floating Footer */
    .floating-footer {
        @apply fixed bottom-0 left-0 right-0 bg-white shadow-lg md:hidden;
    }

    .floating-footer-link {
        @apply flex flex-col items-center space-y-1 text-gray-600;
    }

    .floating-footer-link.active {
        @apply text-primary;
    }

    /* Animations */
    .fade-enter {
        @apply opacity-0;
    }

    .fade-enter-active {
        @apply opacity-100 transition-opacity duration-300;
    }

    .fade-exit {
        @apply opacity-100;
    }

    .fade-exit-active {
        @apply opacity-0 transition-opacity duration-300;
    }

    /* Loading States */
    .loading-overlay {
        @apply absolute inset-0 bg-white/80 flex items-center justify-center;
    }

    .loading-spinner {
        @apply animate-spin rounded-full h-8 w-8 border-4 border-primary border-t-transparent;
    }

    /* QR Code Container */
    .qr-container {
        @apply p-4 bg-white rounded-lg shadow-sm;
    }

    /* Event Card */
    .event-card {
        @apply bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300;
    }

    .event-card-image {
        @apply w-full h-48 object-cover;
    }

    .event-card-content {
        @apply p-4;
    }

    /* Profile Section */
    .profile-header {
        @apply bg-primary h-32;
    }

    .profile-avatar {
        @apply w-32 h-32 rounded-full border-4 border-white bg-white object-cover -mt-16;
    }

    /* Admin Dashboard */
    .stat-card {
        @apply bg-white rounded-xl shadow-sm p-6;
    }

    .stat-icon {
        @apply p-3 rounded-lg;
    }

    /* Responsive Tables */
    .table-responsive {
        @apply min-w-full divide-y divide-gray-200;
    }

    .table-header {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
    }

    .table-cell {
        @apply px-6 py-4 whitespace-nowrap;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    @apply w-2;
}

::-webkit-scrollbar-track {
    @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
    @apply bg-primary/50 rounded-full hover:bg-primary/70;
}

/* Typography */
body {
    @apply font-sans text-gray-800;
}

h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .container {
        @apply px-4;
    }

    .main-content {
        @apply pb-20; /* Space for floating footer */
    }
}

/* Print Styles */
@media print {
    .no-print {
        @apply hidden;
    }

    .print-only {
        @apply block;
    }
}