import './bootstrap';
import Alpine from 'alpinejs';
import AOS from 'aos';
import QRCode from 'qrcode';

// Initialize AlpineJS
window.Alpine = Alpine;
Alpine.start();

// Initialize AOS
AOS.init({
    duration: 800,
    once: true,
    offset: 50,
    easing: 'ease-in-out',
});

// PWA Installation
let deferredPrompt;

window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
    showInstallPromotion();
});

function showInstallPromotion() {
    const installButton = document.getElementById('installButton');
    const installPrompt = document.getElementById('installPrompt');
    
    if (installButton && installPrompt) {
        installPrompt.classList.remove('hidden');
        
        installButton.addEventListener('click', async () => {
            installPrompt.classList.add('hidden');
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                if (outcome === 'accepted') {
                    console.log('User accepted the install prompt');
                }
                deferredPrompt = null;
            }
        });
    }
}

// Service Worker Registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('ServiceWorker registered: ', registration);
            })
            .catch(error => {
                console.log('ServiceWorker registration failed: ', error);
            });
    });
}

// QR Code Generator
window.generateQRCode = async (data, canvas) => {
    try {
        await QRCode.toCanvas(canvas, data, {
            width: 200,
            margin: 1,
            color: {
                dark: '#000000',
                light: '#ffffff'
            }
        });
    } catch (err) {
        console.error('Error generating QR code:', err);
    }
};

// Floating Footer Navigation
const initFloatingNav = () => {
    const nav = document.querySelector('.floating-footer');
    if (!nav) return;

    let lastScroll = window.scrollY;
    
    window.addEventListener('scroll', () => {
        const currentScroll = window.scrollY;
        if (currentScroll > lastScroll && currentScroll > 100) {
            nav.classList.add('translate-y-full');
        } else {
            nav.classList.remove('translate-y-full');
        }
        lastScroll = currentScroll;
    });
};

// Offline Mode Handler
window.addEventListener('online', () => {
    document.body.classList.remove('offline');
    // Sync any pending data
    syncPendingData();
});

window.addEventListener('offline', () => {
    document.body.classList.add('offline');
    showOfflineNotification();
});

function showOfflineNotification() {
    const notification = document.createElement('div');
    notification.className = 'fixed top-0 left-0 right-0 bg-yellow-50 text-yellow-800 px-4 py-2 text-center';
    notification.textContent = 'Anda sedang offline. Beberapa fitur mungkin tidak tersedia.';
    document.body.prepend(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

async function syncPendingData() {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
            type: 'SYNC_PENDING_DATA'
        });
    }
}

// Initialize components
document.addEventListener('DOMContentLoaded', () => {
    initFloatingNav();
    
    // Check if offline on load
    if (!navigator.onLine) {
        document.body.classList.add('offline');
    }
});

// Custom Event Handlers
document.addEventListener('ticket-purchased', (e) => {
    const ticket = e.detail;
    // Cache ticket data for offline access
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
            type: 'CACHE_TICKET',
            ticket: ticket
        });
    }
});