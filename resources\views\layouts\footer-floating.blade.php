<!-- Floating Footer Navigation -->
<nav class="fixed bottom-0 left-0 right-0 bg-white shadow-lg md:hidden">
    <div class="flex justify-around items-center h-16 px-4">
        <!-- Home -->
        <a href="{{ route('home') }}" class="flex flex-col items-center space-y-1 {{ request()->routeIs('home') ? 'text-primary' : 'text-gray-600' }}">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
            </svg>
            <span class="text-xs">Beranda</span>
        </a>

        <!-- My Tickets -->
        <a href="{{ route('tickets.index') }}" class="flex flex-col items-center space-y-1 {{ request()->routeIs('tickets.*') ? 'text-primary' : 'text-gray-600' }}">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
            </svg>
            <span class="text-xs">Tiket Saya</span>
        </a>

        <!-- Add Event (Seller Only) -->
        @if(auth()->check() && auth()->user()->role === 'penjual')
        <a href="{{ route('events.create') }}" class="flex flex-col items-center space-y-1 {{ request()->routeIs('events.create') ? 'text-primary' : 'text-gray-600' }}">
            <div class="bg-primary rounded-full p-3 -mt-8 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                </svg>
            </div>
            <span class="text-xs">Tambah</span>
        </a>
        @endif

        <!-- Notifications -->
        <a href="{{ route('notifications') }}" class="flex flex-col items-center space-y-1 {{ request()->routeIs('notifications') ? 'text-primary' : 'text-gray-600' }}">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
            </svg>
            <span class="text-xs">Notifikasi</span>
        </a>

        <!-- Profile -->
        <a href="{{ route('profile') }}" class="flex flex-col items-center space-y-1 {{ request()->routeIs('profile') ? 'text-primary' : 'text-gray-600' }}">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
            <span class="text-xs">Akun</span>
        </a>
    </div>
</nav>